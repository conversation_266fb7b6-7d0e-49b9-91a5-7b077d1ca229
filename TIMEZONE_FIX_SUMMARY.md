# Timezone Fix for create_dt and update_dt in tsys_user Table

## Problem Description

The `tsys_user` table was experiencing issues with `create_dt` and `update_dt` fields due to improper SQL query construction. The problem was caused by directly embedding SQL expressions into query strings instead of using parameterized queries.

### Original Problematic Code

```typescript
// ❌ PROBLEMATIC: Direct SQL string interpolation
await client.query(`
  INSERT INTO tsys_user (create_dt, update_dt)
  VALUES (${getCurrentThailandTimestamp()}, ${getCurrentThailandTimestamp()})
`, [/* other parameters */]);

// This generated SQL like:
// INSERT INTO tsys_user (create_dt, update_dt) 
// VALUES (CURRENT_TIMESTAMP AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Bangkok', CURRENT_TIMESTAMP AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Bangkok')
```

### Issues with the Original Approach

1. **SQL Syntax Errors**: Complex timezone expressions embedded directly in SQL strings
2. **Parameter Mismatch**: SQL placeholders ($1, $2, etc.) didn't match the actual parameters
3. **Security Risk**: String interpolation can lead to SQL injection vulnerabilities
4. **Maintenance Issues**: Hard to debug and modify timezone logic

## Solution Implemented

### 1. Enhanced Timezone Utility

Added a new function to `src/main/utils/timezone.ts`:

```typescript
/**
 * Get current timestamp as a JavaScript Date object in Thailand timezone
 * @returns Date object representing current time in Thailand timezone
 */
export function getCurrentThailandDate(): Date {
  return new Date(new Date().toLocaleString("en-US", {timeZone: THAILAND_TIMEZONE}));
}
```

### 2. Updated SQL Queries

Replaced all problematic SQL queries with proper parameterized queries:

```typescript
// ✅ FIXED: Proper parameterized queries
const currentTime = getCurrentThailandDate();
await client.query(`
  INSERT INTO tsys_user (create_dt, update_dt)
  VALUES ($7, $8)
`, [/* other parameters */, currentTime, currentTime]);
```

### 3. Files Modified

#### `src/main/utils/timezone.ts`
- Added `getCurrentThailandDate()` function
- Returns proper JavaScript Date object in Thailand timezone

#### `src/main/handler/authHandler.ts`
- Updated all 15 instances of problematic SQL queries
- Replaced `getCurrentThailandTimestamp()` with `getCurrentThailandDate()`
- Fixed parameter numbering in all SQL queries
- Removed unused imports

## Specific Fixes Applied

### 1. Login Failed Logging
```typescript
// Before
VALUES (0, $1, 'LOGIN_FAILED', $2, $3, 'USER_NOT_FOUND', ${getCurrentThailandTimestamp()})

// After  
VALUES (0, $1, 'LOGIN_FAILED', $2, $3, 'USER_NOT_FOUND', $4)
// With parameter: getCurrentThailandDate()
```

### 2. User Updates
```typescript
// Before
SET failed_attempts = $1, locked = $2, update_dt = ${getCurrentThailandTimestamp()}

// After
SET failed_attempts = $1, locked = $2, update_dt = $4
// With parameter: getCurrentThailandDate()
```

### 3. User Creation
```typescript
// Before
VALUES ($1, $2, $3, $4, $5, false, 0, $6, $6, ${getCurrentThailandTimestamp()}, ${getCurrentThailandTimestamp()})

// After
VALUES ($1, $2, $3, $4, $5, false, 0, $6, $6, $7, $8)
// With parameters: currentTime, currentTime
```

## Benefits of the Fix

1. **Proper SQL Syntax**: All queries now use valid parameterized syntax
2. **Type Safety**: Date objects are properly handled by PostgreSQL driver
3. **Security**: Eliminates SQL injection risks from string interpolation
4. **Maintainability**: Easier to debug and modify timezone logic
5. **Performance**: PostgreSQL can better optimize parameterized queries
6. **Consistency**: All timestamp operations now use the same pattern

## Testing

Created `test-timezone-fix.js` to verify the fix:
- ✅ `getCurrentThailandDate()` returns proper Date objects
- ✅ Date objects work correctly as SQL parameters
- ✅ Parameterized queries are properly constructed
- ✅ No more SQL syntax errors

## Verification Steps

1. All SQL queries now use proper parameter placeholders ($1, $2, etc.)
2. All timestamp values are passed as JavaScript Date objects
3. No more direct SQL string interpolation for timestamps
4. Timezone conversion is handled at the JavaScript level before database insertion

The fix ensures that `create_dt` and `update_dt` fields in the `tsys_user` table are properly handled with correct Thailand timezone timestamps while maintaining SQL query safety and performance.
