# Session Management Fix - "User Already Logged In" Issue

## Problem Description

Users were experiencing an issue where after logging out, they would still get a "User Already Logged In" message when trying to log in again. The system was incorrectly detecting active sessions even after successful logout.

### Symptoms
- User logs out successfully
- User tries to log in again immediately
- System shows: "The user admin is already logged in from another session"
- Shows session details from previous login (e.g., "654 minutes ago")
- Forces user to use "force login" to terminate the "existing" session

## Root Cause Analysis

The issue was in the session detection logic in `src/main/handler/authHandler.ts`. The problematic query used to check for active sessions had several flaws:

### Original Problematic Query
```sql
SELECT session_id, create_dt, ip_address
FROM log_history_user
WHERE user_id = $1 
AND action_type = 'LOGIN_SUCCESS'
AND create_dt > (CURRENT_TIMESTAMP - INTERVAL '24 hours')
AND session_id NOT IN (
  SELECT DISTINCT session_id 
  FROM log_history_user 
  WHERE user_id = $1 
  AND action_type = 'LOGOUT' 
  AND create_dt > (CURRENT_TIMESTAMP - INTERVAL '24 hours')
  AND session_id IS NOT NULL
)
```

### Problems with Original Logic

1. **Time Window Limitation**: The logout check was limited to the same 24-hour window as the login check
2. **NOT IN with NULLs**: `NOT IN` can produce unexpected results when NULL values are present
3. **Missing Logout Types**: Only checked for 'LOGOUT' but not 'FORCE_LOGOUT' actions
4. **Logical Flaw**: If a logout happened more than 24 hours ago, it wouldn't be found, making old sessions appear active

## Solution Implemented

### Fixed Session Detection Query
```sql
SELECT l.session_id, l.create_dt, l.ip_address
FROM log_history_user l
WHERE l.user_id = $1 
AND l.action_type = 'LOGIN_SUCCESS'
AND l.create_dt > (CURRENT_TIMESTAMP - INTERVAL '24 hours')
AND NOT EXISTS (
  SELECT 1 
  FROM log_history_user logout_log
  WHERE logout_log.user_id = l.user_id 
  AND logout_log.session_id = l.session_id
  AND logout_log.action_type IN ('LOGOUT', 'FORCE_LOGOUT')
  AND logout_log.create_dt > l.create_dt
)
```

### Key Improvements

1. **NOT EXISTS Instead of NOT IN**: More reliable handling of NULL values
2. **Temporal Logic**: Checks for logout AFTER login (`logout_log.create_dt > l.create_dt`)
3. **No Time Restriction on Logout**: Finds any logout that occurred after the login, regardless of when
4. **Multiple Logout Types**: Includes both 'LOGOUT' and 'FORCE_LOGOUT' actions
5. **Proper Session Lifecycle**: Correctly tracks the relationship between login and logout events

## Files Modified

### 1. `src/main/handler/authHandler.ts`

#### Updated Functions:
- **user-login handler** (lines 180-198): Fixed session detection for login attempts
- **check-user-session-status handler** (lines 461-478): Fixed active session checking
- **force-logout-user handler** (lines 543-559): Fixed bulk session termination
- **user-logout handler** (lines 348-380): Enhanced logging and debugging

#### Changes Made:
- Replaced problematic `NOT IN` subqueries with `NOT EXISTS`
- Added proper temporal logic for session lifecycle
- Enhanced debugging and logging
- Fixed parameter handling in all session-related queries

## Enhanced Debugging

### Added Detailed Logging
```typescript
console.log(`⚠️ User ${user.user_name} already has an active session:`);
console.log(`   Session ID: ${existingSession.session_id}`);
console.log(`   IP Address: ${existingSession.ip_address || 'unknown IP'}`);
console.log(`   Login Time: ${existingSession.create_dt_thai}`);
console.log(`   Minutes Ago: ${sessionAge}`);
```

### Logout Process Tracking
```typescript
console.log('🚪 Processing logout for user:', logoutData.user_id, 'session:', logoutData.session_id);
console.log('✅ User logout logged successfully:', userName, 'logout record ID:', logoutResult.rows[0]?.id);
```

## Test Scenarios

### Scenario 1: Normal Login/Logout Cycle
1. User logs in at 10:00 AM → Creates LOGIN_SUCCESS record
2. User logs out at 11:00 AM → Creates LOGOUT record
3. User tries to log in again → No active session found (logout exists after login)
4. ✅ **Result**: Login succeeds immediately

### Scenario 2: Force Logout Scenario
1. User logs in at 10:00 AM → Creates LOGIN_SUCCESS record
2. Admin force logs out user at 11:00 AM → Creates FORCE_LOGOUT record
3. User tries to log in again → No active session found (force logout exists after login)
4. ✅ **Result**: Login succeeds immediately

### Scenario 3: Multiple Sessions
1. User logs in at 10:00 AM → Creates LOGIN_SUCCESS record
2. User logs in again at 12:00 PM (force login) → Creates FORCE_LOGOUT for old session + new LOGIN_SUCCESS
3. User logs out at 1:00 PM → Creates LOGOUT record
4. User tries to log in again → No active session found
5. ✅ **Result**: Login succeeds immediately

## Benefits of the Fix

1. **Accurate Session State**: System now correctly identifies when sessions are truly active
2. **Improved User Experience**: No more false "already logged in" messages
3. **Proper Session Lifecycle**: Correctly tracks the relationship between login and logout events
4. **Better Debugging**: Enhanced logging helps troubleshoot session issues
5. **Robust Logic**: Handles edge cases like force logouts and old sessions

## Verification Steps

To verify the fix is working:

1. **Login** → Should succeed and create LOGIN_SUCCESS record
2. **Logout** → Should succeed and create LOGOUT record
3. **Login Again** → Should succeed immediately without "already logged in" message
4. **Check Logs** → Should show proper session tracking and no false active sessions

The session management system now properly handles the complete session lifecycle and accurately detects active vs. terminated sessions.
