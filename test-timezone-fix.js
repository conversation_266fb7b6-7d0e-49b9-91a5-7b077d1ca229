// Test script to verify the timezone fix for create_dt and update_dt
console.log('🔍 Testing timezone fix...');

// Simulate the getCurrentThailandDate function
function getCurrentThailandDate() {
  return new Date(new Date().toLocaleString("en-US", {timeZone: 'Asia/Bangkok'}));
}

// Test the getCurrentThailandDate function
try {
  const thailandDate = getCurrentThailandDate();
  console.log('✅ getCurrentThailandDate() works:', thailandDate);
  console.log('   Type:', typeof thailandDate);
  console.log('   Is Date object:', thailandDate instanceof Date);
  console.log('   ISO String:', thailandDate.toISOString());

  // Test that it can be used as a parameter in SQL queries
  console.log('\n📝 Testing SQL parameter usage:');
  const mockParams = [
    'test_user',
    'test_password',
    thailandDate,  // This should work as a parameter
    thailandDate   // This should work as a parameter
  ];

  console.log('✅ Mock SQL parameters:', mockParams);
  console.log('   Parameter 3 (create_dt):', mockParams[2]);
  console.log('   Parameter 4 (update_dt):', mockParams[3]);

  // Test SQL query construction
  console.log('\n🔧 Testing SQL query construction:');
  const oldWay = `INSERT INTO tsys_user (create_dt, update_dt) VALUES (CURRENT_TIMESTAMP AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Bangkok', CURRENT_TIMESTAMP AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Bangkok')`;
  const newWay = `INSERT INTO tsys_user (create_dt, update_dt) VALUES ($1, $2)`;

  console.log('❌ Old way (problematic):', oldWay);
  console.log('✅ New way (fixed):', newWay);
  console.log('   With parameters:', [thailandDate, thailandDate]);

} catch (error) {
  console.error('❌ Error testing timezone fix:', error.message);
}

console.log('\n🎯 Summary:');
console.log('- getCurrentThailandDate() returns a proper Date object');
console.log('- Date objects can be used directly as SQL parameters');
console.log('- This fixes the SQL syntax issues with create_dt and update_dt');
console.log('- Parameterized queries are safer and more reliable');
