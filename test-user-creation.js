// Test script for user creation with encrypted passwords
const bcrypt = require('bcrypt');
const { Client } = require('pg');

async function testPasswordEncryption() {
  console.log('🔐 Testing password encryption...');
  
  const password = 'TestPassword123';
  const saltRounds = 10;
  
  // Hash the password
  const hashedPassword = await bcrypt.hash(password, saltRounds);
  console.log('Original password:', password);
  console.log('Hashed password:', hashedPassword);
  
  // Verify the password
  const isValid = await bcrypt.compare(password, hashedPassword);
  console.log('Password verification result:', isValid);
  
  // Test with wrong password
  const wrongPassword = 'WrongPassword';
  const isWrongValid = await bcrypt.compare(wrongPassword, hashedPassword);
  console.log('Wrong password verification result:', isWrongValid);
}

async function testDatabaseConnection() {
  console.log('🔗 Testing database connection...');
  
  try {
    // You'll need to update these with your actual database credentials
    const client = new Client({
      host: 'localhost', // Update with your DB host
      port: 5432, // Update with your DB port
      database: 'your_database', // Update with your DB name
      user: 'your_username', // Update with your DB user
      password: 'your_password', // Update with your DB password
    });
    
    await client.connect();
    console.log('✅ Database connection successful');
    
    // Test user creation query structure
    const userRef = 'test-user-' + Date.now();
    console.log('User reference:', userRef);
    
    await client.end();
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
  }
}

async function main() {
  await testPasswordEncryption();
  console.log('---');
  await testDatabaseConnection();
}

if (require.main === module) {
  main().catch(console.error);
}
