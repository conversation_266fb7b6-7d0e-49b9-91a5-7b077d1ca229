// Test timezone SQL generation
const THAILAND_TIMEZONE = 'Asia/Bangkok';

function selectThailandTimestamp(columnName) {
  // Extract just the column name for the alias (remove table prefix if exists)
  const aliasName = columnName.includes('.') ? columnName.split('.').pop() : columnName;
  return `${columnName} AT TIME ZONE '${THAILAND_TIMEZONE}' as ${aliasName}_thai`;
}

console.log('Testing timezone SQL generation:');
console.log('1. selectThailandTimestamp("u.last_login"):', selectThailandTimestamp("u.last_login"));
console.log('2. selectThailandTimestamp("create_dt"):', selectThailandTimestamp("create_dt"));
console.log('3. selectThailandTimestamp("u.create_dt"):', selectThailandTimestamp("u.create_dt"));

// Test the actual SQL that would be generated
const testSQL = `
SELECT u.user_id, u.user_ref, u.user_name, u.role_code, r.role_name,
       u.active, u.locked, u.failed_attempts, 
       ${selectThailandTimestamp('u.last_login')},
       ${selectThailandTimestamp('u.create_dt')}, 
       ${selectThailandTimestamp('u.update_dt')}
FROM tsys_user u
LEFT JOIN tmas_user_role r ON u.role_code = r.role_code
ORDER BY u.user_name
`;

console.log('\nGenerated SQL:');
console.log(testSQL);
