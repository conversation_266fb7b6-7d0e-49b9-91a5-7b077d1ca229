import axios from 'axios';
import { ipcMain } from 'electron';
import { Client } from 'pg';
import { PostgreSQLConnectionConfig } from '../db';

interface PostgreSQLConnectionResult {
  success: boolean;
  message: string;
  connectionTime?: number;
  serverVersion?: string;
  error?: string;
}

interface PCloudConnectionConfig {
  username: string;
  password: string;
  region?: 'us' | 'eu';
}

interface PCloudConnectionResult {
  success: boolean;
  message: string;
  connectionTime?: number;
  userInfo?: {
    email: string;
    emailVerified: boolean;
    premium: boolean;
    quota: number;
    usedQuota: number;
    language: string;
    registered: string;
  };
  error?: string;
}

export function setupConnectionHandlers() {
  console.log('Setting up Connection handlers...');

  // PostgreSQL connection test handler
  ipcMain.handle('test-postgresql-connection', async (_event, config: PostgreSQLConnectionConfig): Promise<PostgreSQLConnectionResult> => {
    const startTime = Date.now();
    let client: Client | null = null;

    try {
      console.log('🔍 Testing PostgreSQL connection...');
      console.log(`📍 Host: ${config.host}:${config.port}`);
      console.log(`🗄️ Database: ${config.database}`);
      console.log(`👤 User: ${config.user}`);

      client = new Client({
        host: config.host,
        port: config.port,
        database: config.database,
        user: config.user,
        password: config.password,
        ssl: config.ssl ? { rejectUnauthorized: false } : false,
        connectionTimeoutMillis: 10000,
        query_timeout: 5000,
      });

      await client.connect();
      console.log('✅ PostgreSQL connection established');

      const result = await client.query('SELECT version() as server_version');
      const serverVersion = result.rows[0]?.server_version || 'Unknown';

      const connectionTime = Date.now() - startTime;
      console.log(`⏱️ Connection time: ${connectionTime}ms`);
      console.log(`🔧 Server version: ${serverVersion}`);

      return {
        success: true,
        message: 'PostgreSQL connection successful',
        connectionTime,
        serverVersion: serverVersion.split(' ')[0] + ' ' + serverVersion.split(' ')[1],
      };

    } catch (error: any) {
      const connectionTime = Date.now() - startTime;
      console.error('❌ PostgreSQL connection failed:', error.message);

      let errorMessage = 'Connection failed';

      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'Connection refused - server may be down or unreachable';
      } else if (error.code === 'ENOTFOUND') {
        errorMessage = 'Host not found - check the hostname/IP address';
      } else if (error.code === 'ECONNRESET') {
        errorMessage = 'Connection reset - check network connectivity';
      } else if (error.code === '28P01') {
        errorMessage = 'Authentication failed - check username/password';
      } else if (error.code === '3D000') {
        errorMessage = 'Database does not exist';
      } else if (error.code === '28000') {
        errorMessage = 'Invalid authorization specification';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Connection timeout - server may be slow or unreachable';
      } else {
        errorMessage = error.message;
      }

      return {
        success: false,
        message: errorMessage,
        connectionTime,
        error: error.code || 'UNKNOWN_ERROR',
      };

    } finally {
      if (client) {
        try {
          await client.end();
          console.log('🔌 PostgreSQL connection closed');
        } catch (closeError) {
          console.error('Error closing PostgreSQL connection:', closeError);
        }
      }
    }
  });

  // pCloud connection test handler
  ipcMain.handle('test-pcloud-connection', async (_event, config: PCloudConnectionConfig): Promise<PCloudConnectionResult> => {
    const startTime = Date.now();

    try {
      console.log('🔍 Testing pCloud connection...');
      console.log(`👤 User: ${config.username}`);
      console.log(`🌍 Region: ${config.region || 'auto-detect'}`);

      const apiHost = config.region === 'eu' ? 'eapi.pcloud.com' : 'api.pcloud.com';
      console.log(`📍 API Host: ${apiHost}`);

      const authParams = new URLSearchParams({
        username: config.username,
        password: config.password,
        getauth: '1',
        logout: '1'
      });

      const apiUrl = `https://${apiHost}/userinfo?${authParams.toString()}`;

      console.log('🔗 Making API request...');
      const response = await axios.get(apiUrl, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Electron-pCloud-Test/1.0'
        }
      });

      const connectionTime = Date.now() - startTime;
      console.log(`⏱️ Connection time: ${connectionTime}ms`);

      if (response.data.result === 0) {
        console.log('✅ pCloud connection successful');
        console.log(`📧 Email: ${response.data.email}`);
        console.log(`💎 Premium: ${response.data.premium ? 'Yes' : 'No'}`);
        console.log(`💾 Quota: ${Math.round(response.data.quota / (1024 * 1024 * 1024))}GB`);
        console.log(`📊 Used: ${Math.round(response.data.usedquota / (1024 * 1024 * 1024))}GB`);

        return {
          success: true,
          message: 'pCloud connection successful',
          connectionTime,
          userInfo: {
            email: response.data.email,
            emailVerified: response.data.emailverified,
            premium: response.data.premium,
            quota: response.data.quota,
            usedQuota: response.data.usedquota,
            language: response.data.language,
            registered: response.data.registered
          }
        };

      } else {
        console.error('❌ pCloud API error:', response.data);

        let errorMessage = 'Authentication failed';

        switch (response.data.result) {
          case 1000:
            errorMessage = 'Login required - invalid credentials';
            break;
          case 2000:
            errorMessage = 'Login failed - check username and password';
            break;
          case 4000:
            errorMessage = 'Too many login attempts from this IP address';
            break;
          default:
            errorMessage = `API error: ${response.data.error || 'Unknown error'}`;
        }

        return {
          success: false,
          message: errorMessage,
          connectionTime,
          error: `API_ERROR_${response.data.result}`
        };
      }

    } catch (error: any) {
      const connectionTime = Date.now() - startTime;
      console.error('❌ pCloud connection failed:', error.message);

      let errorMessage = 'Connection failed';

      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'Connection refused - pCloud servers may be unreachable';
      } else if (error.code === 'ENOTFOUND') {
        errorMessage = 'DNS resolution failed - check internet connection';
      } else if (error.code === 'ECONNRESET') {
        errorMessage = 'Connection reset - network connectivity issue';
      } else if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
        errorMessage = 'Connection timeout - pCloud servers may be slow';
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication failed - invalid username or password';
      } else if (error.response?.status === 403) {
        errorMessage = 'Access forbidden - account may be suspended';
      } else if (error.response?.status >= 500) {
        errorMessage = 'pCloud server error - try again later';
      } else {
        errorMessage = error.message;
      }

      return {
        success: false,
        message: errorMessage,
        connectionTime,
        error: error.code || 'UNKNOWN_ERROR'
      };
    }
  });
}
